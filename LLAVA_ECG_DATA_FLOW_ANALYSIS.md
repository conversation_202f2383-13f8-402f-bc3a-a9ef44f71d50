# LLaVA ECG 数据流分析：从原始数据到Token映射

本文档详细分析LLaVA ECG模型如何处理ECG数据，从原始的.dat/.hea文件到最终的token映射。

## 1. ECG数据格式详解

### 1.1 WFDB格式 (.dat + .hea)

**WFDB (WaveForm DataBase)** 是医学信号处理的标准格式：

- **`.hea` (Header文件)**: 包含元数据
  - 采样率、导联数、记录长度
  - 增益、基线、单位信息
  - 导联名称和配置
  
- **`.dat` (Data文件)**: 包含实际的ECG信号数据
  - 二进制格式存储
  - 多导联时间序列数据

**示例.hea文件内容**:
```
00001_hr 12 500 5000
00001_hr.dat 16 1000 0 0 I
00001_hr.dat 16 1000 0 0 II
00001_hr.dat 16 1000 0 0 III
...
# 12导联，500Hz采样率，5000个采样点
```

### 1.2 数据读取过程

```python
# 使用wfdb库读取
data, fields = wfdb.rdsamp(ecg_path)
# data: numpy array, shape = [seq_length, num_channels]
# fields: 包含采样率、导联名等元数据
```

## 2. LLaVA ECG完整数据处理流程

### 2.1 原始数据加载 (train.py:713-724)

```python
# 1. 读取ECG文件
ecg = wfdb.rdsamp(os.path.join(ecg_folder, ecg_file))[0]

# 2. 数据清理
ecg[np.isnan(ecg)] = 0  # 处理NaN值
ecg[np.isinf(ecg)] = 0  # 处理无穷值

# 3. 维度转换: [seq_length, channels] -> [channels, seq_length]
ecg = torch.Tensor(np.transpose(ecg, (1, 0)).astype(np.float32))

# 4. 序列长度标准化
c, length = ecg.shape
seq_length = self.data_args.ecg_seq_length  # 默认5000
if length < seq_length:
    new_ecg = torch.zeros((c, seq_length))
    new_ecg[:, 0:length] = ecg  # 零填充
    ecg = new_ecg
elif length > seq_length:
    ecg = ecg[:, 0:seq_length]  # 截断
```

**关键点**:
- 输入格式: `[seq_length, channels]` (如 [5000, 12])
- 输出格式: `[channels, seq_length]` (如 [12, 5000])
- 标准化长度: 5000个采样点 (10秒@500Hz)

### 2.2 ECG预处理和增强 (ecg_transform.py)

```python
# 1. 标准化 (使用MIMIC-IV统计值)
class Normalize:
    def forward(self, x):
        for i in range(len(self.mean)):
            x[:, i, :] = (x[:, i, :] - self.mean[i]) / self.std[i]

# 2. 序列长度调整
class Resize:
    def forward(self, x):
        b, c, length = x.shape
        if length < self.seq_length:
            new_x = torch.zeros((b, c, self.seq_length))
            new_x[:, :, 0:length] = x
        elif length > self.seq_length:
            new_x = x[:, :, 0:self.seq_length]
        return new_x

# 3. 训练时数据增强
train_transforms = [
    RandomApply([BaselineWander(fs=500)], p=0.5),  # 基线漂移
    RandomApply([CutMix(fs=500)], p=0.5),          # 信号混合
    RandomApply([RandomMasking(fs=500)], p=0.3),   # 随机遮罩
    Normalize(mean=MIMIC_IV_MEAN, std=MIMIC_IV_STD),
    Resize(seq_length=5000)
]
```

### 2.3 ECG特征提取 (ECG Tower)

**架构**: CLIP-style ECG编码器

```python
# clip_encoder.py:47-61
@torch.no_grad()
def forward(self, ecgs):
    # ecgs shape: [batch_size, channels, seq_length]
    # 输入: [B, 12, 5000]
    
    ecg_features = self.ecg_tower(
        ecgs.to(device=self.device, dtype=self.dtype), 
        output_last_transformer_layer=True
    )
    # 输出: [B, num_patches, hidden_size]
    # 例如: [B, 100, 768]
    
    return ecg_features
```

**ECG Tower内部处理**:
1. **Patch Embedding**: 将ECG信号分割成patches
   - Patch size: 50个采样点
   - Patches数量: 5000/50 = 100个patches
   - 每个patch: [12 channels × 50 points] → [hidden_size]

2. **Transformer编码**: 
   - 多层Transformer处理patch序列
   - 输出: [batch_size, 100, 768] (100个patch特征)

### 2.4 多模态投影器 (MM Projector)

```python
# multimodal_projector/builder.py:33-51
def build_ecg_projector(config):
    projector_type = getattr(config, 'mm_projector_type', 'linear')
    
    if projector_type == 'linear':
        return nn.Linear(config.mm_hidden_size, config.hidden_size)
        # 768 -> 4096 (LLaMA hidden size)
    
    elif projector_type == 'mlp2x_gelu':
        return nn.Sequential(
            nn.Linear(config.mm_hidden_size, config.hidden_size),
            nn.GELU(),
            nn.Linear(config.hidden_size, config.hidden_size)
        )
```

**功能**: 将ECG特征映射到LLM的特征空间
- 输入: [B, 100, 768] (ECG特征)
- 输出: [B, 100, 4096] (LLM特征空间)

### 2.5 文本Token化和ECG Token插入

```python
# mm_utils.py:185-204
def tokenizer_ecg_token(prompt, tokenizer, ECG_TOKEN_INDEX):
    # 1. 按<ecg>分割prompt
    prompt_chunks = [tokenizer(chunk).input_ids for chunk in prompt.split('<ecg>')]
    
    # 2. 在分割点插入ECG_TOKEN_INDEX
    input_ids = []
    for x in insert_separator(prompt_chunks, [ECG_TOKEN_INDEX]):
        input_ids.extend(x)
    
    return input_ids
```

**示例**:
```
原始prompt: "请分析这个<ecg>ECG信号"
分割后: ["请分析这个", "ECG信号"]
Token化: [[请, 分, 析, 这, 个], [ECG, 信, 号]]
插入ECG_TOKEN: [请, 分, 析, 这, 个, <ECG_TOKEN>, ECG, 信, 号]
```

### 2.6 多模态融合 (llava_arch.py:145-223)

```python
def prepare_inputs_labels_for_multimodal(self, input_ids, ...):
    # 1. ECG特征提取和投影
    ecg_features = self.encode_ecgs(ecgs)  # [B, 100, 4096]
    
    # 2. 文本embedding
    cur_input_embeds = self.get_model().embed_tokens(cur_input_ids)
    
    # 3. 找到ECG_TOKEN位置并替换
    ecg_token_indices = torch.where(cur_input_ids == ECG_TOKEN_INDEX)[0]
    
    # 4. 构建融合序列
    new_input_embeds = []
    for i, ecg_idx in enumerate(ecg_token_indices):
        # 添加ECG token之前的文本
        new_input_embeds.append(cur_input_embeds[:ecg_idx])
        # 插入ECG特征 (100个token)
        new_input_embeds.append(ecg_features[i])  # [100, 4096]
        # 添加ECG token之后的文本
        new_input_embeds.append(cur_input_embeds[ecg_idx+1:])
    
    return torch.cat(new_input_embeds, dim=0)
```

**融合结果**:
```
原始: [请, 分, 析, 这, 个, <ECG_TOKEN>, ECG, 信, 号]
融合后: [请, 分, 析, 这, 个, ECG_feat_1, ECG_feat_2, ..., ECG_feat_100, ECG, 信, 号]
```

## 3. 关键数据维度变化

```
原始ECG文件 (.dat/.hea)
    ↓ wfdb.rdsamp()
[5000, 12] numpy array (seq_length, channels)
    ↓ transpose + 预处理
[12, 5000] torch.Tensor (channels, seq_length)
    ↓ ECG Tower (patch + transformer)
[100, 768] ECG features (num_patches, ecg_hidden_size)
    ↓ MM Projector
[100, 4096] Projected features (num_patches, llm_hidden_size)
    ↓ 多模态融合
[seq_len, 4096] 融合后的input_embeds
    ↓ LLM处理
Generated text tokens
```

## 4. 重要配置参数

- **seq_length**: 5000 (标准ECG长度)
- **num_channels**: 12 (12导联ECG)
- **patch_size**: 50 (每个patch的采样点数)
- **num_patches**: 100 (5000/50)
- **ecg_hidden_size**: 768 (ECG特征维度)
- **llm_hidden_size**: 4096 (LLaMA特征维度)
- **sampling_rate**: 500Hz

## 5. 与传统图像处理的对比

| 方面 | 图像处理 | ECG处理 |
|------|----------|---------|
| 输入格式 | [H, W, C] | [channels, seq_length] |
| Patch方式 | 2D patches | 1D temporal patches |
| 特征数量 | 通常256-576个 | 100个 |
| 时序性 | 无 | 强时序依赖 |
| 数据增强 | 旋转、裁剪等 | 基线漂移、信号遮罩等 |

这个完整的流程确保了ECG信号能够被有效地转换为LLM可以理解的token表示，实现了医学信号与自然语言的深度融合。
