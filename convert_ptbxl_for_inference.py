#!/usr/bin/env python3
"""
PTB-XL数据格式转换脚本
将PTB-XL数据集转换为LLaVA ECG模型可以使用的格式
"""

import os
import pandas as pd
import json
import argparse
from pathlib import Path
import numpy as np
import ast
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_ptbxl_database(ptbxl_path):
    """
    加载PTB-XL数据库文件
    
    Args:
        ptbxl_path: PTB-XL数据集根目录
        
    Returns:
        pandas.DataFrame: 数据库DataFrame
    """
    database_file = os.path.join(ptbxl_path, "ptbxl_database.csv")
    
    if not os.path.exists(database_file):
        raise FileNotFoundError(f"PTB-XL database file not found: {database_file}")
    
    df = pd.read_csv(database_file, index_col='ecg_id')
    
    # 解析诊断标签
    df.scp_codes = df.scp_codes.apply(lambda x: ast.literal_eval(x))
    
    return df

def get_diagnostic_labels(scp_codes, label_csv_path):
    """
    获取诊断标签的文本描述
    
    Args:
        scp_codes: SCP代码字典
        label_csv_path: 标签CSV文件路径
        
    Returns:
        list: 诊断标签列表
    """
    if not os.path.exists(label_csv_path):
        return []
    
    label_df = pd.read_csv(label_csv_path, index_col=0)
    
    labels = []
    for code, score in scp_codes.items():
        if code in label_df.index:
            description = label_df.loc[code, 'description']
            labels.append(f"{description} (confidence: {score})")
    
    return labels

def create_conversation_from_labels(labels, ecg_id):
    """
    根据诊断标签创建对话
    
    Args:
        labels: 诊断标签列表
        ecg_id: ECG ID
        
    Returns:
        list: 对话列表
    """
    if not labels:
        # 如果没有标签，创建一个通用的分析请求
        return [
            {
                "from": "human",
                "value": "<ecg>\n请分析这个ECG信号，描述其主要特征和可能的诊断。"
            },
            {
                "from": "gpt",
                "value": "我需要仔细分析这个ECG信号的各项特征来提供准确的诊断。"
            }
        ]
    
    # 根据标签创建更具体的对话
    label_text = ", ".join(labels)
    
    conversations = [
        {
            "from": "human",
            "value": "<ecg>\n请分析这个ECG信号，描述其主要特征。"
        },
        {
            "from": "gpt",
            "value": f"根据ECG分析，主要发现包括：{label_text}"
        }
    ]
    
    # 添加一些额外的问题
    if len(labels) > 0:
        conversations.extend([
            {
                "from": "human",
                "value": "这个ECG是否正常？"
            },
            {
                "from": "gpt",
                "value": "根据分析结果，这个ECG显示了一些异常特征，需要进一步的临床评估。" if len(labels) > 0 else "这个ECG基本正常。"
            }
        ])
    
    return conversations

def convert_ptbxl_to_llava_format(ptbxl_path, output_file, split='test', sampling_rate=500, max_samples=None):
    """
    将PTB-XL数据集转换为LLaVA格式
    
    Args:
        ptbxl_path: PTB-XL数据集根目录
        output_file: 输出JSON文件路径
        split: 数据分割 ('train', 'val', 'test', 'all')
        sampling_rate: 采样率 (100 or 500)
        max_samples: 最大样本数
    """
    logger.info(f"Converting PTB-XL dataset from {ptbxl_path}")
    
    # 加载数据库
    df = load_ptbxl_database(ptbxl_path)
    
    # 加载标签描述
    scp_statements_file = os.path.join(ptbxl_path, "scp_statements.csv")
    
    # 根据split筛选数据
    if split == 'test':
        df = df[df.strat_fold == 10]
    elif split == 'train':
        df = df[df.strat_fold != 10]
    elif split == 'val':
        df = df[df.strat_fold == 9]  # 使用fold 9作为验证集
    # split == 'all' 时不筛选
    
    logger.info(f"Selected {len(df)} samples for split: {split}")
    
    # 限制样本数
    if max_samples and len(df) > max_samples:
        df = df.sample(n=max_samples, random_state=42)
        logger.info(f"Limited to {max_samples} samples")
    
    # 转换数据
    llava_data = []
    
    for ecg_id, row in df.iterrows():
        # 确定ECG文件路径
        if sampling_rate == 500:
            ecg_path = row['filename_hr']
        else:
            ecg_path = row['filename_lr']
        
        # 获取诊断标签
        labels = get_diagnostic_labels(row['scp_codes'], scp_statements_file)
        
        # 创建对话
        conversations = create_conversation_from_labels(labels, ecg_id)
        
        # 创建样本
        sample = {
            "id": str(ecg_id),
            "ecg": ecg_path,
            "conversations": conversations,
            "metadata": {
                "age": int(row['age']) if pd.notna(row['age']) else None,
                "sex": int(row['sex']) if pd.notna(row['sex']) else None,
                "height": float(row['height']) if pd.notna(row['height']) else None,
                "weight": float(row['weight']) if pd.notna(row['weight']) else None,
                "nurse": int(row['nurse']) if pd.notna(row['nurse']) else None,
                "site": int(row['site']) if pd.notna(row['site']) else None,
                "device": row['device'] if pd.notna(row['device']) else None,
                "recording_date": row['recording_date'] if pd.notna(row['recording_date']) else None,
                "report": row['report'] if pd.notna(row['report']) else None,
                "scp_codes": row['scp_codes'],
                "strat_fold": int(row['strat_fold'])
            }
        }
        
        llava_data.append(sample)
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(llava_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Converted {len(llava_data)} samples and saved to {output_file}")
    
    return llava_data

def create_simple_inference_dataset(ptbxl_path, output_file, split='test', max_samples=100):
    """
    创建简单的推理数据集（只包含基本信息）
    
    Args:
        ptbxl_path: PTB-XL数据集根目录
        output_file: 输出JSON文件路径
        split: 数据分割
        max_samples: 最大样本数
    """
    logger.info(f"Creating simple inference dataset from {ptbxl_path}")
    
    # 加载数据库
    df = load_ptbxl_database(ptbxl_path)
    
    # 根据split筛选数据
    if split == 'test':
        df = df[df.strat_fold == 10]
    elif split == 'train':
        df = df[df.strat_fold != 10]
    elif split == 'val':
        df = df[df.strat_fold == 9]
    
    # 限制样本数
    if max_samples and len(df) > max_samples:
        df = df.sample(n=max_samples, random_state=42)
    
    # 创建简单的推理数据集
    inference_data = []
    
    for ecg_id, row in df.iterrows():
        sample = {
            "id": str(ecg_id),
            "ecg": row['filename_hr'],  # 使用高采样率文件
            "conversations": [
                {
                    "from": "human",
                    "value": "<ecg>\n请分析这个ECG信号，描述其主要特征和可能的诊断。"
                },
                {
                    "from": "gpt",
                    "value": "我将仔细分析这个ECG信号的特征。"
                }
            ]
        }
        
        inference_data.append(sample)
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(inference_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Created simple inference dataset with {len(inference_data)} samples: {output_file}")
    
    return inference_data

def main():
    parser = argparse.ArgumentParser(description='Convert PTB-XL dataset to LLaVA format')
    parser.add_argument('--ptbxl_path', type=str, required=True,
                       help='PTB-XL dataset root directory')
    parser.add_argument('--output_file', type=str, required=True,
                       help='Output JSON file path')
    parser.add_argument('--split', type=str, choices=['train', 'val', 'test', 'all'], 
                       default='test', help='Data split to convert')
    parser.add_argument('--sampling_rate', type=int, choices=[100, 500], 
                       default=500, help='Sampling rate')
    parser.add_argument('--max_samples', type=int, help='Maximum number of samples')
    parser.add_argument('--simple', action='store_true', 
                       help='Create simple inference dataset')
    
    args = parser.parse_args()
    
    # 检查PTB-XL路径
    if not os.path.exists(args.ptbxl_path):
        logger.error(f"PTB-XL path does not exist: {args.ptbxl_path}")
        return
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output_file)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    try:
        if args.simple:
            create_simple_inference_dataset(
                ptbxl_path=args.ptbxl_path,
                output_file=args.output_file,
                split=args.split,
                max_samples=args.max_samples
            )
        else:
            convert_ptbxl_to_llava_format(
                ptbxl_path=args.ptbxl_path,
                output_file=args.output_file,
                split=args.split,
                sampling_rate=args.sampling_rate,
                max_samples=args.max_samples
            )
        
        logger.info("Conversion completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during conversion: {e}")
        raise

if __name__ == "__main__":
    main()
