#!/usr/bin/env python3
"""
ECG数据格式适配器
用于处理不同格式的ECG数据，使其能够被LLaVA ECG模型使用
"""

import os
import numpy as np
import pandas as pd
import wfdb
import torch
from pathlib import Path
import json
from typing import List, Dict, Tuple, Optional, Union
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ECGDataAdapter:
    """
    ECG数据适配器类
    支持多种ECG数据格式的转换和处理
    """
    
    def __init__(self):
        self.supported_formats = ['mimic_iv', 'ptbxl', 'wfdb', 'numpy', 'csv']
        
    def load_mimic_iv_ecg(self, ecg_path: str, base_folder: str) -> torch.Tensor:
        """
        加载MIMIC-IV格式的ECG数据
        
        Args:
            ecg_path: ECG文件路径 (如: "files/p1896/p18964122/s40067374/40067374")
            base_folder: 基础数据文件夹路径
            
        Returns:
            torch.Tensor: ECG数据张量 [channels, seq_length]
        """
        full_path = os.path.join(base_folder, ecg_path)
        
        try:
            # 使用wfdb读取数据
            data, _ = wfdb.rdsamp(full_path)
            
            # 处理NaN和Inf值
            data[np.isnan(data)] = 0
            data[np.isinf(data)] = 0
            
            # 转换为torch tensor，并转置为 [channels, seq_length]
            ecg_tensor = torch.Tensor(np.transpose(data, (1, 0)).astype(np.float32))
            
            logger.info(f"Loaded MIMIC-IV ECG: {ecg_path}, shape: {ecg_tensor.shape}")
            return ecg_tensor
            
        except Exception as e:
            logger.error(f"Error loading MIMIC-IV ECG {ecg_path}: {e}")
            raise
    
    def load_ptbxl_ecg(self, ecg_path: str, base_folder: str, sampling_rate: int = 500) -> torch.Tensor:
        """
        加载PTB-XL格式的ECG数据
        
        Args:
            ecg_path: ECG文件路径 (如: "records500/00000/00001_hr")
            base_folder: PTB-XL数据集根目录
            sampling_rate: 采样率 (100 或 500)
            
        Returns:
            torch.Tensor: ECG数据张量 [channels, seq_length]
        """
        full_path = os.path.join(base_folder, ecg_path)
        
        try:
            # 使用wfdb读取数据
            data, _ = wfdb.rdsamp(full_path)
            
            # 处理NaN和Inf值
            data[np.isnan(data)] = 0
            data[np.isinf(data)] = 0
            
            # 转换为torch tensor，并转置为 [channels, seq_length]
            ecg_tensor = torch.Tensor(np.transpose(data, (1, 0)).astype(np.float32))
            
            logger.info(f"Loaded PTB-XL ECG: {ecg_path}, shape: {ecg_tensor.shape}")
            return ecg_tensor
            
        except Exception as e:
            logger.error(f"Error loading PTB-XL ECG {ecg_path}: {e}")
            raise
    
    def load_numpy_ecg(self, ecg_path: str) -> torch.Tensor:
        """
        加载numpy格式的ECG数据
        
        Args:
            ecg_path: numpy文件路径
            
        Returns:
            torch.Tensor: ECG数据张量
        """
        try:
            data = np.load(ecg_path)
            
            # 处理NaN和Inf值
            data[np.isnan(data)] = 0
            data[np.isinf(data)] = 0
            
            # 确保数据格式为 [channels, seq_length]
            if data.ndim == 2:
                if data.shape[0] > data.shape[1]:  # 如果是 [seq_length, channels]
                    data = data.T  # 转置为 [channels, seq_length]
            
            ecg_tensor = torch.Tensor(data.astype(np.float32))
            
            logger.info(f"Loaded numpy ECG: {ecg_path}, shape: {ecg_tensor.shape}")
            return ecg_tensor
            
        except Exception as e:
            logger.error(f"Error loading numpy ECG {ecg_path}: {e}")
            raise
    
    def load_csv_ecg(self, ecg_path: str) -> torch.Tensor:
        """
        加载CSV格式的ECG数据
        
        Args:
            ecg_path: CSV文件路径
            
        Returns:
            torch.Tensor: ECG数据张量
        """
        try:
            data = pd.read_csv(ecg_path).values
            
            # 处理NaN和Inf值
            data[np.isnan(data)] = 0
            data[np.isinf(data)] = 0
            
            # 确保数据格式为 [channels, seq_length]
            if data.shape[0] > data.shape[1]:  # 如果是 [seq_length, channels]
                data = data.T  # 转置为 [channels, seq_length]
            
            ecg_tensor = torch.Tensor(data.astype(np.float32))
            
            logger.info(f"Loaded CSV ECG: {ecg_path}, shape: {ecg_tensor.shape}")
            return ecg_tensor
            
        except Exception as e:
            logger.error(f"Error loading CSV ECG {ecg_path}: {e}")
            raise
    
    def auto_detect_format(self, ecg_path: str, base_folder: Optional[str] = None) -> str:
        """
        自动检测ECG数据格式
        
        Args:
            ecg_path: ECG文件路径
            base_folder: 基础文件夹路径
            
        Returns:
            str: 检测到的格式类型
        """
        if base_folder:
            full_path = os.path.join(base_folder, ecg_path)
        else:
            full_path = ecg_path
        
        # 检查文件扩展名
        if ecg_path.endswith('.npy'):
            return 'numpy'
        elif ecg_path.endswith('.csv'):
            return 'csv'
        elif os.path.exists(full_path + '.hea') and os.path.exists(full_path + '.dat'):
            return 'wfdb'
        elif 'files/p' in ecg_path and '/s' in ecg_path:
            return 'mimic_iv'
        elif 'records' in ecg_path:
            return 'ptbxl'
        else:
            return 'unknown'
    
    def load_ecg_data(self, ecg_path: str, base_folder: Optional[str] = None, 
                      format_type: Optional[str] = None, **kwargs) -> torch.Tensor:
        """
        通用ECG数据加载函数
        
        Args:
            ecg_path: ECG文件路径
            base_folder: 基础文件夹路径
            format_type: 强制指定格式类型
            **kwargs: 其他参数
            
        Returns:
            torch.Tensor: ECG数据张量 [channels, seq_length]
        """
        if format_type is None:
            format_type = self.auto_detect_format(ecg_path, base_folder)
        
        logger.info(f"Loading ECG data with format: {format_type}")
        
        if format_type == 'mimic_iv':
            return self.load_mimic_iv_ecg(ecg_path, base_folder)
        elif format_type == 'ptbxl':
            return self.load_ptbxl_ecg(ecg_path, base_folder, **kwargs)
        elif format_type == 'numpy':
            return self.load_numpy_ecg(ecg_path)
        elif format_type == 'csv':
            return self.load_csv_ecg(ecg_path)
        elif format_type == 'wfdb':
            return self.load_mimic_iv_ecg(ecg_path, base_folder or '')
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def preprocess_ecg(self, ecg_tensor: torch.Tensor, target_length: int = 5000) -> torch.Tensor:
        """
        预处理ECG数据
        
        Args:
            ecg_tensor: 输入ECG张量 [channels, seq_length]
            target_length: 目标序列长度
            
        Returns:
            torch.Tensor: 预处理后的ECG张量
        """
        channels, seq_length = ecg_tensor.shape
        
        # 调整序列长度
        if seq_length < target_length:
            # 如果序列太短，用零填充
            new_ecg = torch.zeros((channels, target_length))
            new_ecg[:, :seq_length] = ecg_tensor
            ecg_tensor = new_ecg
        elif seq_length > target_length:
            # 如果序列太长，截断
            ecg_tensor = ecg_tensor[:, :target_length]
        
        logger.info(f"Preprocessed ECG shape: {ecg_tensor.shape}")
        return ecg_tensor

def create_ptbxl_inference_dataset(ptbxl_root: str, output_file: str, 
                                   subset: str = 'sub_class', split: str = 'test'):
    """
    为PTB-XL数据集创建推理用的数据集文件
    
    Args:
        ptbxl_root: PTB-XL数据集根目录
        output_file: 输出JSON文件路径
        subset: 子集类型 ('sub_class', 'super_class', 'form', 'rhythm')
        split: 数据分割 ('train', 'val', 'test')
    """
    # 读取PTB-XL数据库文件
    database_file = os.path.join(ptbxl_root, f"ptbxl_database_{subset}.csv")
    
    if not os.path.exists(database_file):
        raise FileNotFoundError(f"Database file not found: {database_file}")
    
    df = pd.read_csv(database_file)
    
    # 根据split筛选数据
    test_fold = 10
    if split == 'test':
        df = df[df.strat_fold == test_fold]
    elif split == 'train':
        df = df[df.strat_fold != test_fold]
    elif split == 'val':
        # 可以根据需要定义验证集
        df = df[df.strat_fold == 9]  # 例如使用fold 9作为验证集
    
    # 创建推理数据集
    inference_data = []
    
    for idx, row in df.iterrows():
        # 构建ECG文件路径
        ecg_path = row['filename_hr']  # 使用高采样率文件
        
        # 创建一个基本的推理样本
        sample = {
            "id": str(row['ecg_id']),
            "ecg": ecg_path,
            "conversations": [
                {
                    "from": "human",
                    "value": "<ecg>\n请分析这个ECG信号，描述其主要特征和可能的诊断。"
                },
                {
                    "from": "gpt",
                    "value": "我需要分析这个ECG信号的特征。"  # 这里可以根据实际标签生成更具体的回答
                }
            ]
        }
        
        inference_data.append(sample)
    
    # 保存到JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(inference_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Created inference dataset with {len(inference_data)} samples: {output_file}")

# 使用示例
if __name__ == "__main__":
    # 创建数据适配器
    adapter = ECGDataAdapter()
    
    # 示例1: 加载MIMIC-IV格式数据
    try:
        mimic_base = "/data/dataset/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0"
        mimic_ecg_path = "files/p1896/p18964122/s40067374/40067374"
        
        if os.path.exists(mimic_base):
            ecg_data = adapter.load_ecg_data(mimic_ecg_path, mimic_base, format_type='mimic_iv')
            print(f"MIMIC-IV ECG shape: {ecg_data.shape}")
    except Exception as e:
        print(f"MIMIC-IV example failed: {e}")
    
    # 示例2: 为PTB-XL创建推理数据集
    try:
        ptbxl_root = "/data/yuanxiaoyan/project/MERL/finetune/data_split/ptbxl"
        output_file = "ptbxl_inference_dataset.json"
        
        if os.path.exists(ptbxl_root):
            create_ptbxl_inference_dataset(ptbxl_root, output_file, subset='sub_class', split='test')
            print(f"Created PTB-XL inference dataset: {output_file}")
    except Exception as e:
        print(f"PTB-XL example failed: {e}")
