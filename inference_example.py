#!/usr/bin/env python3
"""
LLaVA ECG 模型推理示例脚本
使用预训练的 mm_projector.bin 文件加载模型并进行推理
支持多种ECG数据格式：MIMIC-IV, PTB-XL, numpy, CSV等
"""

import torch
import sys
import os
import json
import argparse
from pathlib import Path

# 添加项目路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(script_dir, 'llava'))

from llava.model.builder import load_pretrained_model
from llava.mm_utils import get_model_name_from_path
from llava.conversation import conv_templates, SeparatorStyle
from llava.constants import IMAGE_TOKEN_INDEX, DEFAULT_IMAGE_TOKEN, DEFAULT_IM_START_TOKEN, DEFAULT_IM_END_TOKEN
import numpy as np
from ecg_data_adapter import ECGDataAdapter

def load_ecg_model(checkpoint_path, base_model_path, model_name="llava"):
    """
    加载LLaVA ECG模型
    
    Args:
        checkpoint_path: checkpoint目录路径 (包含 mm_projector.bin 和 config.json)
        base_model_path: 基础模型路径 (原始的LLaVA模型)
        model_name: 模型名称
    
    Returns:
        tokenizer, model, ecg_processor, context_len
    """
    print(f"Loading model from checkpoint: {checkpoint_path}")
    print(f"Base model: {base_model_path}")
    
    # 加载模型
    tokenizer, model, ecg_processor, context_len = load_pretrained_model(
        model_path=checkpoint_path,
        model_base=base_model_path,
        model_name=model_name,
        load_8bit=False,
        load_4bit=False,
        device_map="auto"
    )
    
    print("Model loaded successfully!")
    return tokenizer, model, ecg_processor, context_len

def prepare_conversation(question, conv_mode="v1"):
    """
    准备对话格式
    
    Args:
        question: 用户问题
        conv_mode: 对话模式
    
    Returns:
        formatted conversation
    """
    conv = conv_templates[conv_mode].copy()
    conv.append_message(conv.roles[0], question)
    conv.append_message(conv.roles[1], None)
    prompt = conv.get_prompt()
    return prompt, conv

def inference_with_ecg(model, tokenizer, ecg_data, question, device="cuda", ecg_seq_length=5000):
    """
    使用ECG数据进行推理

    Args:
        model: 加载的模型
        tokenizer: tokenizer
        ecg_data: ECG数据 (numpy array 或 torch tensor)
        question: 用户问题
        device: 设备
        ecg_seq_length: ECG序列长度

    Returns:
        生成的回答
    """
    model.eval()

    # 准备对话
    prompt, conv = prepare_conversation(question)

    # 处理ECG数据
    if isinstance(ecg_data, np.ndarray):
        ecg_tensor = torch.from_numpy(ecg_data).float()
    else:
        ecg_tensor = ecg_data.float()

    # 确保ECG数据格式正确 [channels, seq_length]
    if ecg_tensor.dim() == 2:
        channels, seq_length = ecg_tensor.shape
        # 如果是 [seq_length, channels] 格式，需要转置
        if channels > 20:  # 假设导联数不会超过20
            ecg_tensor = ecg_tensor.T
            channels, seq_length = ecg_tensor.shape

        # 调整序列长度
        if seq_length < ecg_seq_length:
            new_ecg = torch.zeros((channels, ecg_seq_length))
            new_ecg[:, :seq_length] = ecg_tensor
            ecg_tensor = new_ecg
        elif seq_length > ecg_seq_length:
            ecg_tensor = ecg_tensor[:, :ecg_seq_length]

    # 添加batch维度
    if ecg_tensor.dim() == 2:
        ecg_tensor = ecg_tensor.unsqueeze(0)  # [1, channels, seq_length]

    # 确保ECG数据在正确的设备上
    ecg_tensor = ecg_tensor.to(device)

    # Tokenize输入
    input_ids = tokenizer(prompt, return_tensors='pt').input_ids.to(device)

    # 生成回答
    with torch.no_grad():
        output_ids = model.generate(
            input_ids,
            ecgs=ecg_tensor,
            do_sample=True,
            temperature=0.2,
            max_new_tokens=512,
            use_cache=True
        )

    # 解码输出
    input_token_len = input_ids.shape[1]
    n_diff_input_output = (input_ids != output_ids[:, :input_token_len]).sum().item()
    if n_diff_input_output > 0:
        print(f'[Warning] {n_diff_input_output} output_ids are not the same as the input_ids')

    outputs = tokenizer.batch_decode(output_ids[:, input_token_len:], skip_special_tokens=True)[0]
    outputs = outputs.strip()

    return outputs

def inference_from_dataset(model, tokenizer, dataset_file, data_adapter, base_folder=None,
                          device="cuda", max_samples=None):
    """
    从数据集文件进行批量推理

    Args:
        model: 加载的模型
        tokenizer: tokenizer
        dataset_file: 数据集JSON文件路径
        data_adapter: ECG数据适配器
        base_folder: ECG数据基础文件夹
        device: 设备
        max_samples: 最大处理样本数
    """
    with open(dataset_file, 'r', encoding='utf-8') as f:
        dataset = json.load(f)

    if max_samples:
        dataset = dataset[:max_samples]

    results = []

    for i, sample in enumerate(dataset):
        print(f"Processing sample {i+1}/{len(dataset)}: {sample['id']}")

        try:
            # 加载ECG数据
            ecg_data = data_adapter.load_ecg_data(
                sample['ecg'],
                base_folder=base_folder
            )

            # 获取问题
            question = sample['conversations'][0]['value'].replace('<ecg>\n', '').replace('<ecg>', '')

            # 进行推理
            answer = inference_with_ecg(
                model=model,
                tokenizer=tokenizer,
                ecg_data=ecg_data,
                question=question,
                device=device
            )

            result = {
                'id': sample['id'],
                'ecg_path': sample['ecg'],
                'question': question,
                'predicted_answer': answer,
                'ground_truth': sample['conversations'][1]['value'] if len(sample['conversations']) > 1 else None
            }

            results.append(result)
            print(f"Answer: {answer}")
            print("-" * 50)

        except Exception as e:
            print(f"Error processing sample {sample['id']}: {e}")
            continue

    return results

def main():
    """
    主函数 - 支持多种使用方式
    """
    parser = argparse.ArgumentParser(description='LLaVA ECG Model Inference')
    parser.add_argument('--checkpoint_path', type=str,
                       default="./checkpoints/llava-llama-2-7b-chat-pretrain/checkpoint-800",
                       help='Checkpoint directory path')
    parser.add_argument('--base_model_path', type=str,
                       default="/data/yuanxiaoyan/project/wwyy/Pertrain_M/llava-v1.6-vicuna-7b",
                       help='Base model path')
    parser.add_argument('--mode', type=str, choices=['single', 'dataset'], default='single',
                       help='Inference mode: single ECG or dataset')
    parser.add_argument('--ecg_path', type=str, help='Single ECG file path')
    parser.add_argument('--ecg_base_folder', type=str, help='ECG data base folder')
    parser.add_argument('--dataset_file', type=str, help='Dataset JSON file path')
    parser.add_argument('--question', type=str,
                       default="请分析这个ECG信号，描述其主要特征。",
                       help='Question to ask about the ECG')
    parser.add_argument('--output_file', type=str, help='Output results file')
    parser.add_argument('--max_samples', type=int, help='Maximum samples to process')
    parser.add_argument('--device', type=str, default='auto', help='Device to use')

    args = parser.parse_args()

    # 设置设备
    if args.device == 'auto':
        device = "cuda" if torch.cuda.is_available() else "cpu"
    else:
        device = args.device

    print(f"Using device: {device}")

    # 检查路径
    if not os.path.exists(args.checkpoint_path):
        print(f"Error: Checkpoint path does not exist: {args.checkpoint_path}")
        return

    if not os.path.exists(args.base_model_path):
        print(f"Error: Base model path does not exist: {args.base_model_path}")
        return

    if not os.path.exists(os.path.join(args.checkpoint_path, "mm_projector.bin")):
        print(f"Error: mm_projector.bin not found in {args.checkpoint_path}")
        return

    # 加载模型
    print("Loading model...")
    try:
        tokenizer, model, ecg_processor, context_len = load_ecg_model(
            checkpoint_path=args.checkpoint_path,
            base_model_path=args.base_model_path,
            model_name="llava"
        )
        print("Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return

    # 创建数据适配器
    data_adapter = ECGDataAdapter()

    if args.mode == 'single':
        # 单个ECG推理
        if not args.ecg_path:
            print("Error: --ecg_path is required for single mode")
            return

        try:
            # 加载ECG数据
            ecg_data = data_adapter.load_ecg_data(
                args.ecg_path,
                base_folder=args.ecg_base_folder
            )

            print(f"Question: {args.question}")
            print("Generating answer...")

            # 进行推理
            answer = inference_with_ecg(
                model=model,
                tokenizer=tokenizer,
                ecg_data=ecg_data,
                question=args.question,
                device=device
            )

            print(f"Answer: {answer}")

        except Exception as e:
            print(f"Error during inference: {e}")

    elif args.mode == 'dataset':
        # 数据集批量推理
        if not args.dataset_file:
            print("Error: --dataset_file is required for dataset mode")
            return

        try:
            results = inference_from_dataset(
                model=model,
                tokenizer=tokenizer,
                dataset_file=args.dataset_file,
                data_adapter=data_adapter,
                base_folder=args.ecg_base_folder,
                device=device,
                max_samples=args.max_samples
            )

            # 保存结果
            if args.output_file:
                with open(args.output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"Results saved to: {args.output_file}")

            print(f"Processed {len(results)} samples successfully")

        except Exception as e:
            print(f"Error during dataset inference: {e}")

if __name__ == "__main__":
    main()
