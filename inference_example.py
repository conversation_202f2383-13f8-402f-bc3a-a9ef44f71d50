#!/usr/bin/env python3
"""
LLaVA ECG 模型推理示例脚本
使用预训练的 mm_projector.bin 文件加载模型并进行推理                
"""

import torch
import sys
import os
from llava.model.builder import load_pretrained_model
from llava.mm_utils import get_model_name_from_path
from llava.conversation import conv_templates, SeparatorStyle
from llava.constants import IMAGE_TOKEN_INDEX, DEFAULT_IMAGE_TOKEN, DEFAULT_IM_START_TOKEN, DEFAULT_IM_END_TOKEN
import numpy as np

def load_ecg_model(checkpoint_path, base_model_path, model_name="llava"):
    """
    加载LLaVA ECG模型
    
    Args:
        checkpoint_path: checkpoint目录路径 (包含 mm_projector.bin 和 config.json)
        base_model_path: 基础模型路径 (原始的LLaVA模型)
        model_name: 模型名称
    
    Returns:
        tokenizer, model, ecg_processor, context_len
    """
    print(f"Loading model from checkpoint: {checkpoint_path}")
    print(f"Base model: {base_model_path}")
    
    # 加载模型
    tokenizer, model, ecg_processor, context_len = load_pretrained_model(
        model_path=checkpoint_path,
        model_base=base_model_path,
        model_name=model_name,
        load_8bit=False,
        load_4bit=False,
        device_map="auto"
    )
    
    print("Model loaded successfully!")
    return tokenizer, model, ecg_processor, context_len

def prepare_conversation(question, conv_mode="v1"):
    """
    准备对话格式
    
    Args:
        question: 用户问题
        conv_mode: 对话模式
    
    Returns:
        formatted conversation
    """
    conv = conv_templates[conv_mode].copy()
    conv.append_message(conv.roles[0], question)
    conv.append_message(conv.roles[1], None)
    prompt = conv.get_prompt()
    return prompt, conv

def inference_with_ecg(model, tokenizer, ecg_data, question, device="cuda"):
    """
    使用ECG数据进行推理
    
    Args:
        model: 加载的模型
        tokenizer: tokenizer
        ecg_data: ECG数据 (numpy array 或 torch tensor)
        question: 用户问题
        device: 设备
    
    Returns:
        生成的回答
    """
    model.eval()
    
    # 准备对话
    prompt, conv = prepare_conversation(question)
    
    # 处理ECG数据
    if isinstance(ecg_data, np.ndarray):
        ecg_tensor = torch.from_numpy(ecg_data).float()
    else:
        ecg_tensor = ecg_data.float()
    
    # 确保ECG数据在正确的设备上
    ecg_tensor = ecg_tensor.to(device)
    
    # 添加batch维度如果需要
    if ecg_tensor.dim() == 2:  # [seq_len, channels]
        ecg_tensor = ecg_tensor.unsqueeze(0)  # [1, seq_len, channels]
    
    # Tokenize输入
    input_ids = tokenizer(prompt, return_tensors='pt').input_ids.to(device)
    
    # 生成回答
    with torch.no_grad():
        output_ids = model.generate(
            input_ids,
            ecgs=ecg_tensor,
            do_sample=True,
            temperature=0.2,
            max_new_tokens=512,
            use_cache=True
        )
    
    # 解码输出
    input_token_len = input_ids.shape[1]
    n_diff_input_output = (input_ids != output_ids[:, :input_token_len]).sum().item()
    if n_diff_input_output > 0:
        print(f'[Warning] {n_diff_input_output} output_ids are not the same as the input_ids')
    
    outputs = tokenizer.batch_decode(output_ids[:, input_token_len:], skip_special_tokens=True)[0]
    outputs = outputs.strip()
    
    return outputs

def main():
    """
    主函数 - 使用示例
    """
    # 配置路径 - 请根据您的实际路径修改
    checkpoint_path = "./checkpoints/llava-llama-2-7b-chat-pretrain/checkpoint-800"  # 包含mm_projector.bin的目录
    base_model_path = "/data/yuanxiaoyan/project/wwyy/Pertrain_M/llava-v1.6-vicuna-7b"  # 原始基础模型路径
    
    # 检查路径是否存在
    if not os.path.exists(checkpoint_path):
        print(f"Error: Checkpoint path does not exist: {checkpoint_path}")
        return
    
    if not os.path.exists(base_model_path):
        print(f"Error: Base model path does not exist: {base_model_path}")
        return
    
    if not os.path.exists(os.path.join(checkpoint_path, "mm_projector.bin")):
        print(f"Error: mm_projector.bin not found in {checkpoint_path}")
        return
    
    # 加载模型
    try:
        tokenizer, model, ecg_processor, context_len = load_ecg_model(
            checkpoint_path=checkpoint_path,
            base_model_path=base_model_path,
            model_name="llava"
        )
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # 示例ECG数据 (请替换为您的实际ECG数据)
    # 假设ECG数据形状为 [sequence_length, num_leads]
    # 这里创建一个示例数据
    ecg_data = np.random.randn(5000, 12)  # 5000个时间点，12导联
    
    # 示例问题
    question = "请分析这个ECG信号，描述其主要特征。"
    
    print(f"Question: {question}")
    print("Generating answer...")
    
    # 进行推理
    try:
        answer = inference_with_ecg(
            model=model,
            tokenizer=tokenizer,
            ecg_data=ecg_data,
            question=question,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
        print(f"Answer: {answer}")
        
    except Exception as e:
        print(f"Error during inference: {e}")

if __name__ == "__main__":
    main()
