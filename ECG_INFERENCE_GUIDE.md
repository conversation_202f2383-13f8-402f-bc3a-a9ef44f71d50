# LLaVA ECG 模型推理指南

本指南将帮助您使用训练好的LLaVA ECG模型在不同格式的下游数据上进行推理。

## 问题分析

您遇到的问题是数据格式不匹配：
- **训练数据格式**: MIMIC-IV (`/data/dataset/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0/files/p1999/p19995790/s48529159`)
- **下游数据格式**: PTB-XL (`/data/yuanxiaoyan/project/MERL/finetune/data_split/ptbxl/sub_class`)

## 解决方案

我们提供了完整的数据格式适配和推理解决方案：

### 1. 数据格式适配器 (`ecg_data_adapter.py`)

支持多种ECG数据格式：
- MIMIC-IV格式
- PTB-XL格式  
- Numpy数组
- CSV文件
- 通用WFDB格式

### 2. 推理脚本 (`inference_example.py`)

支持单个ECG文件推理和批量数据集推理。

### 3. PTB-XL格式转换工具 (`convert_ptbxl_for_inference.py`)

将PTB-XL数据集转换为LLaVA可用的JSON格式。

## 使用步骤

### 步骤1: 测试模型加载

首先确认模型可以正确加载：

```bash
python test_model_loading.py
```

### 步骤2: 转换PTB-XL数据集

将您的PTB-XL数据转换为推理格式：

```bash
# 创建简单的推理数据集（推荐）
python convert_ptbxl_for_inference.py \
    --ptbxl_path /data/yuanxiaoyan/project/MERL/finetune/data_split/ptbxl \
    --output_file ptbxl_inference_test.json \
    --split test \
    --max_samples 10 \
    --simple

# 或创建完整的数据集（包含标签信息）
python convert_ptbxl_for_inference.py \
    --ptbxl_path /data/yuanxiaoyan/project/MERL/finetune/data_split/ptbxl \
    --output_file ptbxl_inference_full.json \
    --split test \
    --sampling_rate 500 \
    --max_samples 100
```

### 步骤3: 进行推理

#### 单个ECG文件推理

```bash
python inference_example.py \
    --mode single \
    --ecg_path "records500/00000/00001_hr" \
    --ecg_base_folder /data/yuanxiaoyan/project/MERL/finetune/data_split/ptbxl \
    --question "请分析这个ECG信号，描述其主要特征。"
```

#### 批量数据集推理

```bash
python inference_example.py \
    --mode dataset \
    --dataset_file ptbxl_inference_test.json \
    --ecg_base_folder /data/yuanxiaoyan/project/MERL/finetune/data_split/ptbxl \
    --output_file inference_results.json \
    --max_samples 5
```

## 详细参数说明

### inference_example.py 参数

- `--checkpoint_path`: checkpoint目录路径（包含mm_projector.bin）
- `--base_model_path`: 原始基础模型路径
- `--mode`: 推理模式 (`single` 或 `dataset`)
- `--ecg_path`: 单个ECG文件路径
- `--ecg_base_folder`: ECG数据基础文件夹
- `--dataset_file`: 数据集JSON文件路径
- `--question`: 要问的问题
- `--output_file`: 输出结果文件
- `--max_samples`: 最大处理样本数
- `--device`: 使用的设备 (`auto`, `cuda`, `cpu`)

### convert_ptbxl_for_inference.py 参数

- `--ptbxl_path`: PTB-XL数据集根目录
- `--output_file`: 输出JSON文件路径
- `--split`: 数据分割 (`train`, `val`, `test`, `all`)
- `--sampling_rate`: 采样率 (100 或 500)
- `--max_samples`: 最大样本数
- `--simple`: 创建简单推理数据集（推荐用于测试）

## 数据格式说明

### MIMIC-IV格式
```
files/p1896/p18964122/s40067374/40067374
├── 40067374.dat  # 数据文件
└── 40067374.hea  # 头文件
```

### PTB-XL格式
```
records500/00000/00001_hr
├── 00001_hr.dat  # 数据文件
└── 00001_hr.hea  # 头文件
```

### JSON数据集格式
```json
[
  {
    "id": "1",
    "ecg": "records500/00000/00001_hr",
    "conversations": [
      {
        "from": "human",
        "value": "<ecg>\n请分析这个ECG信号，描述其主要特征。"
      },
      {
        "from": "gpt",
        "value": "我将仔细分析这个ECG信号的特征。"
      }
    ]
  }
]
```

## 常见问题解决

### 1. 模型加载失败
- 检查checkpoint路径是否正确
- 确认mm_projector.bin文件存在
- 验证base_model_path是否正确

### 2. ECG数据加载失败
- 检查ECG文件路径是否正确
- 确认.dat和.hea文件都存在
- 验证base_folder路径设置

### 3. 内存不足
- 减少max_samples数量
- 使用较小的batch size
- 考虑使用CPU推理

### 4. 数据格式不匹配
- 使用ECGDataAdapter自动检测格式
- 手动指定format_type参数
- 检查数据维度是否正确

## 性能优化建议

1. **GPU使用**: 确保CUDA可用以加速推理
2. **批处理**: 对于大量数据，分批处理避免内存溢出
3. **数据预处理**: 预先转换数据格式以提高效率
4. **模型量化**: 考虑使用8bit或4bit量化减少内存使用

## 示例输出

```
Loading model...
Model loaded successfully!
Processing sample 1/5: 1
Loaded PTB-XL ECG: records500/00000/00001_hr, shape: torch.Size([12, 5000])
Question: 请分析这个ECG信号，描述其主要特征。
Answer: 根据ECG分析，这是一个正常的窦性心律，心率约为75次/分钟。QRS波群形态正常，PR间期和QT间期在正常范围内。未发现明显的ST段改变或T波异常。整体来看，这是一个正常的12导联心电图。
--------------------------------------------------
```

## 技术支持

如果遇到问题，请检查：
1. 所有依赖包是否正确安装
2. 数据路径是否正确
3. 模型文件是否完整
4. GPU内存是否充足

更多详细信息请参考代码注释和错误日志。
