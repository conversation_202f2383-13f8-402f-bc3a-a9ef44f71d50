#!/usr/bin/env python3
"""
测试LLaVA ECG模型加载
验证checkpoint是否可以正确加载
"""

import torch
import os
import sys

# 添加项目路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(script_dir, 'llava'))

from llava.model.builder import load_pretrained_model

def test_model_loading():
    """
    测试模型加载功能
    """
    # 配置路径
    checkpoint_path = "./checkpoints/llava-llama-2-7b-chat-pretrain/checkpoint-800"
    base_model_path = "/data/yuanxiaoyan/project/wwyy/Pertrain_M/llava-v1.6-vicuna-7b"
    
    print("=" * 50)
    print("LLaVA ECG Model Loading Test")
    print("=" * 50)
    
    # 检查文件是否存在
    print("Checking files...")
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ Checkpoint directory not found: {checkpoint_path}")
        return False
    
    mm_projector_path = os.path.join(checkpoint_path, "mm_projector.bin")
    config_path = os.path.join(checkpoint_path, "config.json")
    
    if not os.path.exists(mm_projector_path):
        print(f"❌ mm_projector.bin not found: {mm_projector_path}")
        return False
    else:
        print(f"✅ Found mm_projector.bin: {mm_projector_path}")
    
    if not os.path.exists(config_path):
        print(f"❌ config.json not found: {config_path}")
        return False
    else:
        print(f"✅ Found config.json: {config_path}")
    
    if not os.path.exists(base_model_path):
        print(f"❌ Base model not found: {base_model_path}")
        return False
    else:
        print(f"✅ Found base model: {base_model_path}")
    
    # 检查mm_projector.bin文件大小
    mm_projector_size = os.path.getsize(mm_projector_path) / (1024 * 1024)  # MB
    print(f"📊 mm_projector.bin size: {mm_projector_size:.2f} MB")
    
    # 尝试加载模型
    print("\nLoading model...")
    try:
        tokenizer, model, ecg_processor, context_len = load_pretrained_model(
            model_path=checkpoint_path,
            model_base=base_model_path,
            model_name="llava",
            load_8bit=False,
            load_4bit=False,
            device_map="auto"
        )
        
        print("✅ Model loaded successfully!")
        
        # 显示模型信息
        print(f"📋 Context length: {context_len}")
        print(f"📋 Tokenizer vocab size: {len(tokenizer)}")
        
        # 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 Total parameters: {total_params:,}")
        print(f"📊 Trainable parameters: {trainable_params:,}")
        
        # 检查mm_projector是否正确加载
        if hasattr(model, 'mm_projector'):
            print("✅ mm_projector found in model")
            mm_projector_params = sum(p.numel() for p in model.mm_projector.parameters())
            print(f"📊 mm_projector parameters: {mm_projector_params:,}")
        else:
            print("❌ mm_projector not found in model")
        
        # 检查ECG tower
        if hasattr(model, 'get_ecg_tower') and model.get_ecg_tower() is not None:
            print("✅ ECG tower found in model")
        else:
            print("⚠️  ECG tower not found (this might be expected)")
        
        print("\n" + "=" * 50)
        print("✅ Model loading test PASSED!")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_example():
    """
    显示使用示例
    """
    print("\n" + "=" * 50)
    print("Usage Example")
    print("=" * 50)
    
    example_code = '''
# 基本加载方式
from llava.model.builder import load_pretrained_model

checkpoint_path = "./checkpoints/llava-llama-2-7b-chat-pretrain/checkpoint-800"
base_model_path = "/data/yuanxiaoyan/project/wwyy/Pertrain_M/llava-v1.6-vicuna-7b"

tokenizer, model, ecg_processor, context_len = load_pretrained_model(
    model_path=checkpoint_path,        # 您的checkpoint路径
    model_base=base_model_path,        # 原始基础模型路径
    model_name="llava",
    device_map="auto"
)

# 现在您可以使用model进行推理了
'''
    
    print(example_code)

if __name__ == "__main__":
    success = test_model_loading()
    
    if success:
        show_usage_example()
    else:
        print("\n" + "=" * 50)
        print("❌ Model loading test FAILED!")
        print("Please check the paths and try again.")
        print("=" * 50)
